/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #14b8a6;
    --primary-dark: #0f766e;
    --secondary-color: #f97316;
    --accent-color: #06b6d4;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --background: #ffffff;
    --surface: #f8fafc;
    --border: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --gradient-primary: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%);
    --gradient-secondary: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-logo .logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-cta {
    background: var(--primary-color);
    color: white !important;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.nav-cta:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-cta::after {
    display: none;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    overflow: hidden;
    padding-top: 80px;
}

/* Background Elements */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(20, 184, 166, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(249, 115, 22, 0.05) 0%, transparent 50%);
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(20, 184, 166, 0.1), rgba(249, 115, 22, 0.1));
    animation: float 8s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    top: 10%;
    right: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-30px) rotate(180deg);
        opacity: 0.6;
    }
}

/* Hero Container */
.hero-container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 2rem 1rem;
    display: grid;
    grid-template-columns: 1.1fr 0.9fr;
    gap: 4rem;
    align-items: center;
    min-height: 80vh;
}

.hero-content {
    animation: slideInUp 1s ease-out;
}

/* Hero Badge */
.hero-badge {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, rgba(20, 184, 166, 0.1), rgba(249, 115, 22, 0.1));
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    border-radius: 60px;
    border: 1px solid rgba(20, 184, 166, 0.2);
    margin-bottom: 2.5rem;
    animation: fadeInUp 1s ease-out 0.2s both;
    overflow: hidden;
}

.badge-icon {
    width: 24px;
    height: 24px;
    color: var(--primary-color);
    animation: pulse 2s ease-in-out infinite;
}

.badge-text {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: 0.5px;
}

.badge-pulse {
    position: absolute;
    top: 50%;
    left: 1rem;
    width: 24px;
    height: 24px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translateY(-50%);
    opacity: 0.3;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: translateY(-50%) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-50%) scale(1.2);
        opacity: 0.6;
    }
}

/* Hero Title */
.hero-title {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
}

.title-main {
    display: block;
    color: var(--text-primary);
    animation: slideInUp 0.8s ease-out 0.4s both;
}

.title-highlight {
    display: block;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: slideInUp 0.8s ease-out 0.6s both;
    position: relative;
}

.title-highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0.2;
    border-radius: 4px;
    animation: expandWidth 1s ease-out 1.2s both;
}

.title-subtitle {
    display: block;
    font-size: clamp(1.2rem, 3vw, 1.8rem);
    font-weight: 600;
    color: var(--text-secondary);
    animation: slideInUp 0.8s ease-out 0.8s both;
}

@keyframes expandWidth {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

/* Hero Description */
.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 500px;
    font-weight: 400;
    animation: fadeInUp 1s ease-out 1s both;
}

/* Hero Features */
.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 1.2s both;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(20, 184, 166, 0.1);
    border-radius: 50px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(20, 184, 166, 0.15);
    border-color: rgba(20, 184, 166, 0.3);
}

.feature-icon {
    width: 20px;
    height: 20px;
    color: var(--primary-color);
}

.feature-item span {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Hero Stats */
.hero-stats {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1rem 0;
    animation: fadeInUp 1s ease-out 1.6s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
    display: block;
    line-height: 1;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-divider {
    width: 1px;
    height: 40px;
    background: linear-gradient(to bottom, transparent, var(--border), transparent);
}

/* Animation Keyframes */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(60px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(60px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2.5rem;
    animation: fadeInUp 1s ease-out 1.4s both;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: 0 10px 30px rgba(20, 184, 166, 0.3);
}

.btn-primary:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 40px rgba(20, 184, 166, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    border: 2px solid rgba(20, 184, 166, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 40px rgba(20, 184, 166, 0.3);
}

.btn-arrow,
.btn-icon {
    width: 20px;
    height: 20px;
    stroke-width: 2.5;
    transition: transform 0.3s ease;
}

.btn:hover .btn-arrow {
    transform: translateX(4px);
}

.btn:hover .btn-icon {
    transform: scale(1.1);
}

/* Partners Section */
.partners-section {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 2;
    animation: fadeInUp 1s ease-out 1.8s both;
}

.partners-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.partners-logos {
    display: flex;
    gap: 3rem;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.partner-logo {
    height: 35px;
    width: auto;
    opacity: 0.6;
    transition: all 0.4s ease;
    filter: grayscale(100%) brightness(0.8);
}

.partner-logo:hover {
    opacity: 1;
    filter: grayscale(0%) brightness(1);
    transform: scale(1.1);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    animation: slideInRight 1s ease-out 0.8s both;
}

.image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 500px;
}

.image-wrapper {
    position: relative;
    z-index: 2;
    border-radius: 24px;
    overflow: hidden;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    animation: imageFloat 6s ease-in-out infinite;
}

.hero-image {
    width: 380px;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.image-wrapper:hover .hero-image {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(20, 184, 166, 0.1) 0%,
        rgba(249, 115, 22, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-wrapper:hover .image-overlay {
    opacity: 1;
}

@keyframes imageFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-15px) rotate(1deg);
    }
    66% {
        transform: translateY(-5px) rotate(-1deg);
    }
}



/* Info Cards */
.info-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    pointer-events: none;
}

.info-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1rem;
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: cardFloat 8s ease-in-out infinite;
    pointer-events: auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 180px;
}

.info-card:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(20, 184, 166, 0.2);
}

.card-1 {
    top: 10%;
    right: -15%;
    animation-delay: 1s;
}

.card-2 {
    top: 50%;
    left: -20%;
    animation-delay: 3s;
}

.card-3 {
    bottom: 15%;
    right: -10%;
    animation-delay: 5s;
}

.info-card .card-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.info-card .card-icon svg {
    width: 20px;
    height: 20px;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.2rem;
    line-height: 1.2;
}

.card-subtitle {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    line-height: 1.3;
}

@keyframes cardFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-15px) rotate(1deg);
    }
    50% {
        transform: translateY(-5px) rotate(0deg);
    }
    75% {
        transform: translateY(-10px) rotate(-1deg);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}





/* Responsive Design */
@media (max-width: 1200px) {
    .hero-container {
        gap: 3rem;
        padding: 2rem 2rem 1rem;
    }

    .hero-image {
        width: 350px;
    }

    .image-container {
        height: 450px;
    }
}

@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
        min-height: 70vh;
    }

    .hero-visual {
        order: -1;
    }

    .hero-image {
        width: 320px;
    }

    .image-container {
        height: 380px;
    }

    .hero-features {
        justify-content: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .info-cards .info-card {
        display: none;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-container {
        padding: 1.5rem 1rem;
        gap: 2rem;
        min-height: 60vh;
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .title-subtitle {
        font-size: clamp(1rem, 3vw, 1.4rem);
    }

    .hero-description {
        font-size: 1.1rem;
        max-width: 100%;
    }

    .hero-features {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn {
        width: 100%;
        max-width: 320px;
        justify-content: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    .stat-divider {
        display: none;
    }

    .hero-image {
        width: 280px;
    }

    .image-container {
        height: 320px;
    }

    .partners-logos {
        gap: 2rem;
    }

    .partner-logo {
        height: 32px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 1rem;
    }

    .hero-container {
        padding: 1.5rem 1rem;
        gap: 2rem;
    }

    .hero-badge {
        padding: 0.75rem 1.5rem;
        gap: 0.75rem;
    }

    .badge-text {
        font-size: 0.85rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .feature-item {
        padding: 0.5rem 1rem;
    }

    .feature-item span {
        font-size: 0.8rem;
    }

    .btn {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .hero-image {
        width: 280px;
    }

    .image-container {
        height: 350px;
    }

    .partners-logos {
        gap: 1.5rem;
        flex-wrap: wrap;
    }

    .partner-logo {
        height: 28px;
    }

    .partners-section {
        bottom: 1rem;
    }
}
