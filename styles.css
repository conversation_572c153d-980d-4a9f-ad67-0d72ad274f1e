/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --background: #ffffff;
    --surface: #f8fafc;
    --border: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-logo .logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-cta {
    background: var(--primary-color);
    color: white !important;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.nav-cta:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-cta::after {
    display: none;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: #ffffff;
    padding-top: 80px;
    overflow: hidden;
}

.hero-container {
    position: relative;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
    min-height: calc(100vh - 80px);
}

.hero-content {
    animation: slideInLeft 1s ease-out;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #e0f2fe, #f3e5f5);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid #e3f2fd;
    box-shadow: 0 4px 20px rgba(37, 99, 235, 0.1);
}

.badge-icon {
    font-size: 1.2rem;
}

.hero-title {
    font-size: clamp(3.5rem, 8vw, 6rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.title-line {
    display: block;
    animation: slideInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(50px);
}

.title-line:nth-child(1) {
    animation-delay: 0.2s;
}

.title-line:nth-child(2) {
    animation-delay: 0.4s;
}

.highlight {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.3rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 550px;
    line-height: 1.7;
    animation: fadeIn 1s ease-out 0.8s both;
}

.hero-stats {
    display: flex;
    gap: 3rem;
    margin-bottom: 3rem;
    animation: fadeIn 1s ease-out 1s both;
}

.stat-item {
    text-align: center;
    position: relative;
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
    display: block;
}

.stat-label {
    font-size: 0.95rem;
    color: var(--text-secondary);
    font-weight: 500;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 4rem;
    animation: fadeIn 1s ease-out 1.2s both;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.25rem 2.5rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: inherit;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.btn-icon {
    width: 22px;
    height: 22px;
    stroke-width: 2;
}

/* Hero Partners */
.hero-partners {
    animation: fadeIn 1s ease-out 1.4s both;
}

.partners-text {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.partners-logos {
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
}

.partner-logo {
    height: 40px;
    width: auto;
    opacity: 0.7;
    transition: all 0.3s ease;
    filter: grayscale(100%);
}

.partner-logo:hover {
    opacity: 1;
    filter: grayscale(0%);
    transform: scale(1.05);
}

/* Hero Visual */
.hero-visual {
    animation: slideInRight 1s ease-out 0.5s both;
    position: relative;
}

.visual-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;
}

.main-image {
    position: relative;
    z-index: 2;
}

.hero-image {
    width: 450px;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    animation: mainImageFloat 4s ease-in-out infinite;
}

@keyframes mainImageFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-15px);
    }
}

.floating-images {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.floating-img {
    position: absolute;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    animation: floatImage 6s ease-in-out infinite;
}

.float-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.img-1 {
    width: 120px;
    height: 120px;
    top: 10%;
    left: -10%;
    animation-delay: 0s;
}

.img-2 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: -5%;
    animation-delay: 2s;
}

.img-3 {
    width: 140px;
    height: 140px;
    bottom: 5%;
    left: 5%;
    animation-delay: 4s;
}

@keyframes floatImage {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
}

.feature-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    pointer-events: none;
}

.feature-card {
    position: absolute;
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: cardFloat 5s ease-in-out infinite;
    pointer-events: auto;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-1 {
    top: 15%;
    right: -15%;
    animation-delay: 1s;
}

.card-2 {
    top: 45%;
    left: -20%;
    animation-delay: 3s;
}

.card-3 {
    bottom: 20%;
    right: -10%;
    animation-delay: 5s;
}

.feature-card .card-icon {
    font-size: 2rem;
    background: linear-gradient(135deg, #e0f2fe, #f3e5f5);
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-content h4 {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.card-content p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
}

@keyframes cardFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}



/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 20px;
    height: 20px;
    border-right: 2px solid var(--text-light);
    border-bottom: 2px solid var(--text-light);
    transform: rotate(45deg);
}

.scroll-text {
    font-size: 0.8rem;
    font-weight: 500;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-container {
        gap: 4rem;
    }

    .hero-image {
        width: 380px;
    }

    .visual-container {
        height: 500px;
    }
}

@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 4rem;
        text-align: center;
    }

    .hero-visual {
        order: -1;
    }

    .hero-image {
        width: 350px;
    }

    .hero-stats {
        justify-content: center;
    }

    .feature-cards .feature-card {
        display: none;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-container {
        padding: 0 1rem;
        gap: 3rem;
    }

    .hero-title {
        font-size: clamp(2.5rem, 10vw, 4rem);
    }

    .hero-description {
        font-size: 1.1rem;
        max-width: 100%;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: 2rem;
    }

    .partners-logos {
        justify-content: center;
        gap: 1.5rem;
    }

    .partner-logo {
        height: 35px;
    }

    .hero-image {
        width: 300px;
    }

    .visual-container {
        height: 400px;
    }

    .floating-images .floating-img {
        display: none;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 1rem;
    }

    .hero-container {
        gap: 2rem;
    }

    .hero-badge {
        font-size: 0.8rem;
        padding: 0.6rem 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats {
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.85rem;
    }

    .hero-image {
        width: 250px;
    }

    .visual-container {
        height: 350px;
    }

    .partners-logos {
        gap: 1rem;
    }

    .partner-logo {
        height: 30px;
    }
}
