/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --background: #ffffff;
    --surface: #f8fafc;
    --border: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-logo .logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-cta {
    background: var(--primary-color);
    color: white !important;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.nav-cta:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-cta::after {
    display: none;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(37, 99, 235, 0.9) 0%, 
        rgba(16, 185, 129, 0.8) 50%, 
        rgba(245, 158, 11, 0.7) 100%);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-element {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;
}

.element-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.element-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

.element-5 {
    width: 40px;
    height: 40px;
    top: 40%;
    left: 5%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

.hero-container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: 100vh;
}

.hero-content {
    animation: slideInLeft 1s ease-out;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.badge-icon {
    font-size: 1.2rem;
}

.hero-title {
    font-size: clamp(3rem, 8vw, 5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: white;
}

.title-line {
    display: block;
    animation: slideInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(50px);
}

.title-line:nth-child(1) {
    animation-delay: 0.2s;
}

.title-line:nth-child(2) {
    animation-delay: 0.4s;
}

.title-line:nth-child(3) {
    animation-delay: 0.6s;
}

.highlight {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 500px;
    line-height: 1.7;
    animation: fadeIn 1s ease-out 0.8s both;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    animation: fadeIn 1s ease-out 1s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    animation: fadeIn 1s ease-out 1.2s both;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
}

.btn-icon {
    width: 20px;
    height: 20px;
    stroke-width: 2;
}

/* Hero Partners */
.hero-partners {
    animation: fadeIn 1s ease-out 1.4s both;
}

.partners-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.partners-logos {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.partner-logo {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Hero Visual */
.hero-visual {
    animation: slideInRight 1s ease-out 0.5s both;
    position: relative;
}

.visual-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.device-mockup {
    width: 300px;
    height: 600px;
    background: linear-gradient(145deg, #ffffff, #f0f0f0);
    border-radius: 30px;
    padding: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5);
    position: relative;
    animation: deviceFloat 3s ease-in-out infinite;
}

.device-screen {
    width: 100%;
    height: 100%;
    background: #000;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.screen-content {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.health-dashboard {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.dashboard-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.user-info {
    color: white;
}

.user-name {
    font-weight: 600;
    font-size: 1rem;
}

.user-status {
    font-size: 0.8rem;
    opacity: 0.8;
}

.dashboard-metrics {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    flex: 1;
}

.metric-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.metric-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.metric-value {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
}

@keyframes deviceFloat {
    0%, 100% {
        transform: translateY(0px) rotateY(0deg);
    }
    50% {
        transform: translateY(-10px) rotateY(5deg);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Floating Cards */
.floating-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: cardFloat 4s ease-in-out infinite;
}

.card-1 {
    top: 15%;
    right: -10%;
    animation-delay: 0s;
}

.card-2 {
    top: 45%;
    left: -15%;
    animation-delay: 1.5s;
}

.card-3 {
    bottom: 20%;
    right: -5%;
    animation-delay: 3s;
}

.card-icon {
    font-size: 1.5rem;
}

.card-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
}

@keyframes cardFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
    }
    25% {
        transform: translateY(-10px) translateX(5px);
    }
    50% {
        transform: translateY(0px) translateX(10px);
    }
    75% {
        transform: translateY(10px) translateX(5px);
    }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 20px;
    height: 20px;
    border-right: 2px solid rgba(255, 255, 255, 0.8);
    border-bottom: 2px solid rgba(255, 255, 255, 0.8);
    transform: rotate(45deg);
}

.scroll-text {
    font-size: 0.8rem;
    font-weight: 500;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-visual {
        order: -1;
    }

    .device-mockup {
        width: 250px;
        height: 500px;
    }

    .hero-stats {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-container {
        padding: 0 1rem;
        gap: 2rem;
    }

    .hero-title {
        font-size: clamp(2.5rem, 10vw, 4rem);
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .partners-logos {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .device-mockup {
        width: 200px;
        height: 400px;
    }

    .floating-cards .card {
        display: none;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 1rem;
    }

    .hero-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }
}
