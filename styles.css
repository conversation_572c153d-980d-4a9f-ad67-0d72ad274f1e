/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #14b8a6;
    --primary-dark: #0f766e;
    --secondary-color: #f97316;
    --accent-color: #06b6d4;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --background: #ffffff;
    --surface: #f8fafc;
    --border: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --gradient-primary: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%);
    --gradient-secondary: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-logo .logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-cta {
    background: var(--primary-color);
    color: white !important;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.nav-cta:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-cta::after {
    display: none;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all 0.3s ease;
}

/* Hero Section - Complete Makeover */
.hero {
    position: relative;
    min-height: 100vh;
    background: #ffffff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Animated Background */
.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.bg-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(20, 184, 166, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(20, 184, 166, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at top left, rgba(20, 184, 166, 0.1) 0%, transparent 50%),
                radial-gradient(ellipse at bottom right, rgba(249, 115, 22, 0.1) 0%, transparent 50%);
}

.floating-orbs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.orb {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(20, 184, 166, 0.2), rgba(249, 115, 22, 0.2));
    filter: blur(40px);
    animation: orbFloat 15s ease-in-out infinite;
}

.orb-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 300px;
    height: 300px;
    top: 50%;
    right: 10%;
    animation-delay: 5s;
}

.orb-3 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 30%;
    animation-delay: 10s;
}

@keyframes gridMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(50px, 50px);
    }
}

@keyframes orbFloat {
    0%, 100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translate(30px, -30px) scale(1.1);
        opacity: 0.6;
    }
}

/* Hero Wrapper */
.hero-wrapper {
    position: relative;
    z-index: 2;
    flex: 1;
    display: flex;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 6rem 2rem 4rem;
    gap: 6rem;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    animation: slideInLeft 1s ease-out;
}

/* Announcement Bar */
.announcement-bar {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, rgba(20, 184, 166, 0.1), rgba(249, 115, 22, 0.1));
    border: 1px solid rgba(20, 184, 166, 0.2);
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    margin-bottom: 2rem;
    animation: slideInUp 0.8s ease-out 0.2s both;
    transition: all 0.3s ease;
    cursor: pointer;
}

.announcement-bar:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(20, 184, 166, 0.15);
}

.announcement-icon {
    width: 18px;
    height: 18px;
    color: var(--secondary-color);
    animation: sparkle 2s ease-in-out infinite;
}

.announcement-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: 0.3px;
}

.announcement-arrow {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 600;
    transition: transform 0.3s ease;
}

.announcement-bar:hover .announcement-arrow {
    transform: translateX(4px);
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
    }
    50% {
        transform: scale(1.1) rotate(180deg);
    }
}

/* Headline Section */
.headline-section {
    margin-bottom: 3rem;
}

.main-headline {
    font-size: clamp(3rem, 7vw, 5.5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.03em;
}

.headline-line {
    display: block;
    animation: slideInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

.headline-line:nth-child(1) {
    color: var(--text-primary);
    animation-delay: 0.4s;
}

.headline-line:nth-child(2) {
    animation-delay: 0.6s;
}

.headline-line:nth-child(3) {
    color: var(--text-primary);
    animation-delay: 0.8s;
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.gradient-text::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0.3;
    border-radius: 3px;
    animation: expandWidth 1s ease-out 1.4s both;
}

.sub-headline {
    font-size: 1.25rem;
    color: var(--text-secondary);
    line-height: 1.6;
    font-weight: 400;
    animation: fadeInUp 1s ease-out 1s both;
}

@keyframes expandWidth {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

/* Trust Indicators */
.trust-indicators {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 1.2s both;
}

.trust-item {
    text-align: center;
}

.trust-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.trust-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* CTA Section */
.cta-section {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 1.4s both;
}

.cta-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 16px;
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 25px rgba(20, 184, 166, 0.3);
}

.cta-primary:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 40px rgba(20, 184, 166, 0.4);
}

.cta-icon {
    width: 20px;
    height: 20px;
    stroke-width: 2.5;
    transition: transform 0.3s ease;
}

.cta-primary:hover .cta-icon {
    transform: translateX(4px);
}

.cta-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: transparent;
    color: var(--text-primary);
    border: 2px solid rgba(20, 184, 166, 0.2);
    border-radius: 16px;
    padding: 1.25rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cta-secondary:hover {
    border-color: var(--primary-color);
    background: rgba(20, 184, 166, 0.05);
    transform: translateY(-2px);
}

.cta-play {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.cta-play svg {
    width: 16px;
    height: 16px;
    margin-left: 2px;
}

/* Hero Visual */
.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: slideInRight 1s ease-out 0.8s both;
}

.visual-wrapper {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.device-container {
    position: relative;
    z-index: 2;
}

.device-frame {
    position: relative;
    border-radius: 24px;
    overflow: hidden;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    animation: deviceFloat 6s ease-in-out infinite;
}

.device-image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.device-frame:hover .device-image {
    transform: scale(1.02);
}

.device-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    right: -50%;
    bottom: -50%;
    background: radial-gradient(ellipse, rgba(20, 184, 166, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.device-frame:hover .device-glow {
    opacity: 1;
}

@keyframes deviceFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(1deg);
    }
    66% {
        transform: translateY(-5px) rotate(-1deg);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-wrapper {
        gap: 4rem;
        padding: 5rem 2rem 3rem;
    }

    .main-headline {
        font-size: clamp(2.5rem, 6vw, 4.5rem);
    }
}

@media (max-width: 1024px) {
    .hero-wrapper {
        flex-direction: column;
        gap: 3rem;
        text-align: center;
        padding: 4rem 2rem 3rem;
    }

    .hero-visual {
        order: -1;
    }

    .trust-indicators {
        justify-content: center;
    }

    .cta-section {
        justify-content: center;
    }

    .floating-metrics .metric-card {
        display: none;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-wrapper {
        padding: 3rem 1rem 2rem;
        gap: 2rem;
    }

    .main-headline {
        font-size: clamp(2rem, 8vw, 3.5rem);
    }

    .sub-headline {
        font-size: 1.1rem;
    }

    .trust-indicators {
        flex-wrap: wrap;
        gap: 1.5rem;
    }

    .cta-section {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .cta-primary,
    .cta-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .feature-pills {
        justify-content: center;
    }

    .partners-grid {
        gap: 2rem;
    }

    .floating-orbs {
        display: none;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 1rem;
    }

    .hero-wrapper {
        padding: 2rem 1rem;
    }

    .announcement-bar {
        padding: 0.6rem 1.2rem;
        gap: 0.5rem;
    }

    .announcement-text {
        font-size: 0.8rem;
    }

    .trust-indicators {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .trust-number {
        font-size: 1.5rem;
    }

    .cta-primary,
    .cta-secondary {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .partners-grid {
        gap: 1.5rem;
    }

    .partner-logo {
        height: 30px;
    }
}

/* Feature Pills */
.feature-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    animation: fadeInUp 1s ease-out 1.6s both;
}

.pill {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(20, 184, 166, 0.15);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-primary);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.pill:hover {
    transform: translateY(-2px);
    border-color: rgba(20, 184, 166, 0.3);
    box-shadow: 0 4px 15px rgba(20, 184, 166, 0.1);
}

.pill-icon {
    font-size: 1rem;
}

/* Animation Keyframes */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-60px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(60px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Status Indicator */
.status-indicator {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(20, 184, 166, 0.2);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    animation: statusPulse 3s ease-in-out infinite;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: dotPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes dotPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.2);
    }
}

/* Floating Metrics */
.floating-metrics {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    pointer-events: none;
}

.metric-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1rem;
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: metricFloat 8s ease-in-out infinite;
    pointer-events: auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
}

.metric-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(20, 184, 166, 0.2);
}

.metric-1 {
    top: 15%;
    right: -10%;
    animation-delay: 1s;
}

.metric-2 {
    top: 50%;
    left: -15%;
    animation-delay: 3s;
}

.metric-3 {
    bottom: 20%;
    right: -5%;
    animation-delay: 5s;
}

.metric-icon {
    font-size: 1.5rem;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.metric-value {
    font-size: 1.1rem;
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: 0.2rem;
}

.metric-label {
    font-size: 0.7rem;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@keyframes metricFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-10px) rotate(1deg);
    }
    50% {
        transform: translateY(-5px) rotate(0deg);
    }
    75% {
        transform: translateY(-8px) rotate(-1deg);
    }
}



/* Partners Strip */
.partners-strip {
    background: rgba(248, 250, 252, 0.8);
    border-top: 1px solid rgba(20, 184, 166, 0.1);
    padding: 2rem 0;
    margin-top: auto;
}

.partners-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.partners-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.partners-grid {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.partner-logo {
    height: 35px;
    width: auto;
    opacity: 0.6;
    transition: all 0.3s ease;
    filter: grayscale(100%) brightness(0.8);
}

.partner-logo:hover {
    opacity: 1;
    filter: grayscale(0%) brightness(1);
    transform: scale(1.1);
}



@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}





/* Responsive Design */
@media (max-width: 1200px) {
    .hero-container {
        gap: 3rem;
        padding: 2rem 2rem 1rem;
    }

    .hero-image {
        width: 350px;
    }

    .image-container {
        height: 450px;
    }
}

@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
        min-height: 70vh;
    }

    .hero-visual {
        order: -1;
    }

    .hero-image {
        width: 320px;
    }

    .image-container {
        height: 380px;
    }

    .hero-features {
        justify-content: center;
    }

    .hero-stats {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-container {
        padding: 1.5rem 1rem;
        gap: 2rem;
        min-height: 60vh;
    }

    .decorative-elements {
        display: none;
    }

    .pattern-overlay {
        display: none;
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .title-subtitle {
        font-size: clamp(1rem, 3vw, 1.4rem);
    }

    .hero-description {
        font-size: 1.1rem;
        max-width: 100%;
    }

    .hero-features {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn {
        width: 100%;
        max-width: 320px;
        justify-content: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    .stat-divider {
        display: none;
    }

    .hero-image {
        width: 280px;
    }

    .image-container {
        height: 320px;
    }

    .partners-logos {
        gap: 2rem;
    }

    .partner-logo {
        height: 32px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 1rem;
    }

    .hero-container {
        padding: 1.5rem 1rem;
        gap: 2rem;
    }

    .hero-badge {
        padding: 0.75rem 1.5rem;
        gap: 0.75rem;
    }

    .badge-text {
        font-size: 0.85rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .feature-item {
        padding: 0.5rem 1rem;
    }

    .feature-item span {
        font-size: 0.8rem;
    }

    .btn {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .hero-image {
        width: 280px;
    }

    .image-container {
        height: 350px;
    }

    .partners-logos {
        gap: 1.5rem;
        flex-wrap: wrap;
    }

    .partner-logo {
        height: 28px;
    }

    .partners-section {
        bottom: 1rem;
    }
}
